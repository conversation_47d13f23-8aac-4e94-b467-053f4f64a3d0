#!/usr/bin/env python3
"""
Test script to verify parsing fixes are working.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_parsing_fixes():
    """Test the fixed parsing logic."""
    print("🔧 Testing Parsing Fixes")
    print("=" * 50)
    
    try:
        from src.jira.parser import FirewallTableParser
        
        parser = FirewallTableParser()
        
        # Test Case 1: Jira Table Format
        print("\n--- Test 1: Jira Table Format ---")
        jira_issue = {
            'key': 'TEST-001',
            'summary': 'Test Jira table parsing',
            'description': '''
            Firewall Change Request
            
            ||Field||Value||
            |Source|*************|
            |Destination|*********|
            |Port|443|
            |Protocol|HTTPS|
            |Action|Allow|
            |Business Justification|Web server access for customer portal|
            '''
        }
        
        request = parser.parse_issue(jira_issue)
        print(f"Rules found: {len(request.rules)}")
        print(f"Parsing errors: {len(request.parsing_errors)}")
        
        if request.parsing_errors:
            print("Errors:")
            for error in request.parsing_errors:
                print(f"  - {error}")
        
        if request.rules:
            rule = request.rules[0]
            print(f"✅ Rule parsed successfully:")
            print(f"  Source: {rule.source}")
            print(f"  Destination: {rule.destination}")
            print(f"  Port: {rule.port}")
            print(f"  Protocol: {rule.protocol}")
            print(f"  Action: {rule.action}")
            print(f"  Justification: {rule.justification}")
        else:
            print("❌ No rules parsed")
        
        # Test Case 2: Markdown Table Format
        print("\n--- Test 2: Markdown Table Format ---")
        markdown_issue = {
            'key': 'TEST-002',
            'summary': 'Test markdown table parsing',
            'description': '''
            Firewall Change Request
            
            | Field | Value |
            |-------|-------|
            | Source | ************* |
            | Destination | ********* |
            | Port | 443 |
            | Protocol | HTTPS |
            | Action | Allow |
            | Business Justification | Web server access |
            '''
        }
        
        request = parser.parse_issue(markdown_issue)
        print(f"Rules found: {len(request.rules)}")
        print(f"Parsing errors: {len(request.parsing_errors)}")
        
        if request.parsing_errors:
            print("Errors:")
            for error in request.parsing_errors:
                print(f"  - {error}")
        
        if request.rules:
            rule = request.rules[0]
            print(f"✅ Rule parsed successfully:")
            print(f"  Source: {rule.source}")
            print(f"  Destination: {rule.destination}")
            print(f"  Port: {rule.port}")
            print(f"  Protocol: {rule.protocol}")
        else:
            print("❌ No rules parsed")
        
        # Test Case 3: Field-Value Pairs
        print("\n--- Test 3: Field-Value Pairs ---")
        field_value_issue = {
            'key': 'TEST-003',
            'summary': 'Test field-value parsing',
            'description': '''
            Firewall Change Request
            
            Source: *************
            Destination: *********
            Port: 443
            Protocol: HTTPS
            Action: Allow
            Business Justification: Web server access for customer portal
            '''
        }
        
        request = parser.parse_issue(field_value_issue)
        print(f"Rules found: {len(request.rules)}")
        print(f"Parsing errors: {len(request.parsing_errors)}")
        
        if request.parsing_errors:
            print("Errors:")
            for error in request.parsing_errors:
                print(f"  - {error}")
        
        if request.rules:
            rule = request.rules[0]
            print(f"✅ Rule parsed successfully:")
            print(f"  Source: {rule.source}")
            print(f"  Destination: {rule.destination}")
            print(f"  Port: {rule.port}")
            print(f"  Protocol: {rule.protocol}")
        else:
            print("❌ No rules parsed")
        
        # Test Case 4: Debug raw table data
        print("\n--- Test 4: Debug Raw Table Data ---")
        debug_issue = {
            'key': 'TEST-004',
            'summary': 'Debug parsing',
            'description': '''
            ||Field||Value||
            |Source|*************|
            |Destination|*********|
            |Port|443|
            |Protocol|HTTPS|
            '''
        }
        
        request = parser.parse_issue(debug_issue)
        print(f"Raw table data: {len(request.raw_table_data)} items")
        for i, item in enumerate(request.raw_table_data):
            print(f"  {i+1}. Field: '{item['field']}' -> Value: '{item['value']}'")
        
        print(f"Rules found: {len(request.rules)}")
        print(f"Parsing errors: {len(request.parsing_errors)}")
        
        if request.parsing_errors:
            print("Errors:")
            for error in request.parsing_errors:
                print(f"  - {error}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_field_classification():
    """Test field classification logic."""
    print("\n🏷️  Testing Field Classification")
    print("=" * 50)
    
    try:
        from src.jira.parser import FirewallTableParser, FieldType
        
        parser = FirewallTableParser()
        
        test_fields = [
            ("Source", FieldType.SOURCE),
            ("source", FieldType.SOURCE),
            ("Source Address", FieldType.SOURCE),
            ("Destination", FieldType.DESTINATION),
            ("destination", FieldType.DESTINATION),
            ("Dest", FieldType.DESTINATION),
            ("Port", FieldType.PORT),
            ("port", FieldType.PORT),
            ("Protocol", FieldType.PROTOCOL),
            ("protocol", FieldType.PROTOCOL),
            ("Action", FieldType.ACTION),
            ("action", FieldType.ACTION),
            ("Business Justification", FieldType.JUSTIFICATION),
            ("justification", FieldType.JUSTIFICATION),
            ("Random Field", FieldType.OTHER)
        ]
        
        for field_name, expected_type in test_fields:
            actual_type = parser._classify_field(field_name)
            status = "✅" if actual_type == expected_type else "❌"
            print(f"{status} '{field_name}' -> {actual_type.value} (expected: {expected_type.value})")
        
        return True
        
    except Exception as e:
        print(f"❌ Field classification test failed: {e}")
        return False

def main():
    """Run parsing tests."""
    print("🚀 Firewall Parser Fix Test")
    print("=" * 60)
    
    tests = [
        ("Parsing Fixes", test_parsing_fixes),
        ("Field Classification", test_field_classification)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name} - PASSED")
            else:
                print(f"\n❌ {test_name} - FAILED")
        except Exception as e:
            print(f"\n💥 {test_name} - ERROR: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All parsing tests passed! The fixes are working.")
        print("\nYou can now run:")
        print("  python quick_test.py")
        print("  python debug_test_runner.py")
        print("  python interactive_debug.py")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
