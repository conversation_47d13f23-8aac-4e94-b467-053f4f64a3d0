#!/usr/bin/env python3
"""
Simple Firewall Change Analyzer
===============================
A streamlined script for analyzing Jira firewall change requests and providing
automated security recommendations.

This script consolidates all functionality into a single file for easy understanding
and maintenance. It performs the following workflow:
1. Connects to Jira API to retrieve issue data
2. Parses firewall table information from issue description
3. Validates rules against built-in security standards
4. Optionally queries Tufin API for existing rule analysis
5. Generates security recommendations based on findings
6. Posts results back to <PERSON>ra as comments

Usage:
    python simple_firewall_analyzer.py --jira-issue FW-123 [--dry-run]

Environment Variables Required:
    JIRA_URL - Your Jira instance URL (e.g., https://company.atlassian.net)
    JIRA_USERNAME - Your Jira username or email
    JIRA_API_TOKEN - Jira API token (generate from Jira settings)

    Optional (for Tufin integration):
    TUFIN_URL - Tufin SecureTrack API URL
    TUFIN_USERNAME - Tufin username
    TUFIN_PASSWORD - Tufin password

Author: Firewall Automation Team
Version: 1.0 (Simplified)
"""

# Standard library imports for core functionality
import os          # For environment variable access
import sys         # For exit codes and system operations
import re          # For regular expression pattern matching
import json        # For JSON data handling (if needed)
import argparse    # For command-line argument parsing
import requests    # For HTTP API calls to Jira and Tufin

# Type hints for better code documentation and IDE support
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class FirewallRule:
    """
    Data structure representing a single firewall rule.

    This class holds all the essential information about a firewall rule
    that we extract from Jira issue descriptions.

    Attributes:
        source (str): Source IP address, network, or 'any'
        destination (str): Destination IP address, network, or 'any'
        port (str): Port number, range, or service name
        protocol (str): Network protocol (TCP, UDP, HTTP, SSH, etc.)
        action (str): Rule action, defaults to 'allow'

    Example:
        rule = FirewallRule(
            source="10.0.0.0/24",
            destination="***********00",
            port="80",
            protocol="HTTP",
            action="allow"
        )
    """
    source: str
    destination: str
    port: str
    protocol: str
    action: str = "allow"  # Default action if not specified


@dataclass
class SecurityViolation:
    """
    Data structure representing a security standard violation.

    When a firewall rule doesn't meet security standards, we create
    a SecurityViolation object to track the details.

    Attributes:
        rule_index (int): Which rule number (0-based) has the violation
        field (str): Which field has the issue (source, destination, port, etc.)
        value (str): The actual value that caused the violation
        issue (str): Description of what security issue was detected
        severity (str): How serious the violation is (CRITICAL, HIGH, MEDIUM, LOW)
        recommendation (str): Suggested fix for the violation

    Example:
        violation = SecurityViolation(
            rule_index=0,
            field="source",
            value="any",
            issue="Overly permissive source",
            severity="HIGH",
            recommendation="Restrict source to specific IP ranges"
        )
    """
    rule_index: int
    field: str
    value: str
    issue: str
    severity: str
    recommendation: str


class SimpleFirewallAnalyzer:
    """
    Main class that handles all firewall change analysis functionality.

    This class encapsulates all the logic for:
    - Connecting to external APIs (Jira, Tufin)
    - Parsing firewall rules from Jira issues
    - Validating rules against security standards
    - Generating and posting recommendations

    The class is designed to be simple and self-contained, with all
    configuration coming from environment variables.
    """

    def __init__(self):
        """
        Initialize the analyzer with configuration from environment variables.

        This constructor reads all necessary configuration from environment
        variables and sets up the security standards that will be used
        for validation.

        Environment Variables Used:
            JIRA_URL: Base URL for Jira instance
            JIRA_USERNAME: Username for Jira authentication
            JIRA_API_TOKEN: API token for Jira authentication
            TUFIN_URL: Base URL for Tufin API (optional)
            TUFIN_USERNAME: Username for Tufin authentication (optional)
            TUFIN_PASSWORD: Password for Tufin authentication (optional)
        """
        # Load Jira configuration from environment variables
        # These are required for the script to function
        self.jira_url = os.getenv('JIRA_URL')
        self.jira_username = os.getenv('JIRA_USERNAME')
        self.jira_token = os.getenv('JIRA_API_TOKEN')

        # Load Tufin configuration from environment variables
        # These are optional - script will work without Tufin integration
        self.tufin_url = os.getenv('TUFIN_URL')
        self.tufin_username = os.getenv('TUFIN_USERNAME')
        self.tufin_password = os.getenv('TUFIN_PASSWORD')

        # Define security standards as patterns to detect risky configurations
        # Each pattern has a regex to match against and a severity level
        # You can modify these patterns to add/remove/change security checks
        self.risky_patterns = {
            # Detect overly permissive source addresses
            'any_source': {
                'pattern': r'(any|0\.0\.0\.0/0|\*)',  # Matches 'any', '0.0.0.0/0', or '*'
                'severity': 'HIGH'
            },
            # Detect overly permissive destination addresses
            'any_destination': {
                'pattern': r'(any|0\.0\.0\.0/0|\*)',  # Matches 'any', '0.0.0.0/0', or '*'
                'severity': 'HIGH'
            },
            # Detect SSH protocol usage (can be risky if not properly secured)
            'ssh_protocol': {
                'pattern': r'(ssh|22)',  # Matches 'ssh' or port '22'
                'severity': 'MEDIUM'
            },
            # Detect RDP protocol usage (high risk for external access)
            'rdp_protocol': {
                'pattern': r'(rdp|3389)',  # Matches 'rdp' or port '3389'
                'severity': 'HIGH'
            },
            # Detect Telnet protocol usage (critical - unencrypted)
            'telnet_protocol': {
                'pattern': r'(telnet|23)',  # Matches 'telnet' or port '23'
                'severity': 'CRITICAL'
            },
            # Detect FTP protocol usage (medium risk - often unencrypted)
            'ftp_protocol': {
                'pattern': r'(ftp|21)',  # Matches 'ftp' or port '21'
                'severity': 'MEDIUM'
            },
            # Detect wide port ranges (can expose too many services)
            'wide_port_range': {
                'pattern': r'(\d+)-(\d+)',  # Matches patterns like '1000-2000'
                'severity': 'MEDIUM'
            }
        }

    def validate_config(self) -> bool:
        """
        Validate that all required configuration is present.

        This method checks that the minimum required environment variables
        are set for the script to function. Jira credentials are required,
        while Tufin credentials are optional.

        Returns:
            bool: True if configuration is valid, False otherwise

        Note:
            If this returns False, the script should exit as it cannot
            connect to Jira to retrieve issues.
        """
        # Check if all required Jira configuration is present
        if not all([self.jira_url, self.jira_username, self.jira_token]):
            print("❌ Missing required Jira configuration")
            print("Required environment variables:")
            print("  JIRA_URL - Your Jira instance URL")
            print("  JIRA_USERNAME - Your Jira username")
            print("  JIRA_API_TOKEN - Your Jira API token")
            return False

        print("✅ Jira configuration validated")

        # Check if Tufin configuration is present (optional)
        if all([self.tufin_url, self.tufin_username, self.tufin_password]):
            print("✅ Tufin configuration found - will include Tufin analysis")
        else:
            print("ℹ️  Tufin configuration not complete - will skip Tufin analysis")

        return True

    def get_jira_issue(self, issue_key: str) -> Optional[Dict]:
        """
        Retrieve issue data from Jira using the REST API.

        This method connects to Jira and fetches the complete issue data
        including the description field where firewall rules are expected
        to be documented.

        Args:
            issue_key (str): The Jira issue key (e.g., 'FW-123', 'PROJ-456')

        Returns:
            Optional[Dict]: The complete Jira issue data as a dictionary,
                          or None if the request failed

        API Details:
            - Uses Jira REST API v2
            - Authenticates with username and API token
            - Fetches all issue fields including description
            - Has 30-second timeout to prevent hanging
        """
        print(f"📥 Retrieving Jira issue: {issue_key}")

        # Construct the Jira REST API URL for the specific issue
        url = f"{self.jira_url}/rest/api/2/issue/{issue_key}"

        # Set up HTTP Basic Authentication using username and API token
        auth = (self.jira_username, self.jira_token)

        try:
            # Make the HTTP GET request to Jira
            response = requests.get(url, auth=auth, timeout=30)

            # Raise an exception if the HTTP request failed (4xx or 5xx status)
            response.raise_for_status()

            # Parse the JSON response and return it
            issue_data = response.json()
            print(f"✅ Successfully retrieved issue: {issue_data.get('key', 'Unknown')}")
            return issue_data

        except requests.RequestException as e:
            # Handle any HTTP-related errors (network, authentication, etc.)
            print(f"❌ Failed to retrieve Jira issue: {e}")
            print(f"   Check that the issue key '{issue_key}' exists and you have access to it")
            return None

    def parse_firewall_table(self, issue_data: Dict) -> List[FirewallRule]:
        """
        Parse firewall rules from the Jira issue description.

        This method handles both ADF (Atlassian Document Format) and plain text
        descriptions to extract firewall rule information. It supports multiple
        formats including tables and structured text.

        Supported Formats:
            1. ADF Table format (Jira Cloud v3 API)
            2. Plain text table format (| Field | Value |)
            3. Structured text format (Field: Value)

        Args:
            issue_data (Dict): The complete Jira issue data from the API

        Returns:
            List[FirewallRule]: List of parsed firewall rules, empty if none found

        Parsing Logic:
            1. Extract description field from issue data
            2. Handle ADF format if present, otherwise use plain text
            3. Use multiple regex patterns to find Field|Value patterns
            4. Group consecutive field/value pairs into rules
            5. Create FirewallRule objects from the grouped data
        """
        print("🔍 Parsing firewall rules from issue description")

        # Extract the description field from the Jira issue data
        description_field = issue_data.get('fields', {}).get('description', '')

        # Handle ADF (Atlassian Document Format) vs plain text
        description_text = self._extract_text_from_description(description_field)

        if not description_text:
            print("⚠️  No description found in issue")
            return []

        print(f"📄 Description length: {len(description_text)} characters")

        rules = []

        # Try multiple parsing strategies
        rules = self._parse_table_format(description_text)
        if not rules:
            rules = self._parse_structured_format(description_text)

        print(f"✅ Successfully parsed {len(rules)} firewall rules")
        return rules

    def _extract_text_from_description(self, description_field) -> str:
        """
        Extract plain text from Jira description field.

        Handles both ADF (Atlassian Document Format) and plain text formats.

        Args:
            description_field: The description field from Jira API response

        Returns:
            str: Plain text content extracted from the description
        """
        if not description_field:
            return ""

        # If it's already a string (plain text), return as-is
        if isinstance(description_field, str):
            print("📝 Processing plain text description")
            return description_field

        # If it's ADF format (dict with type, version, content)
        if isinstance(description_field, dict) and description_field.get('type') == 'doc':
            print("📝 Processing ADF (Atlassian Document Format) description")
            return self._extract_text_from_adf(description_field)

        # Fallback: convert to string
        return str(description_field)

    def _extract_text_from_adf(self, adf_content: Dict) -> str:
        """
        Extract plain text from ADF (Atlassian Document Format) content.

        Recursively traverses ADF structure to extract text content,
        preserving table structure and formatting where possible.

        Args:
            adf_content (Dict): ADF document structure

        Returns:
            str: Plain text representation of the ADF content
        """
        if not isinstance(adf_content, dict):
            return str(adf_content)

        text_parts = []

        # Handle different ADF node types
        node_type = adf_content.get('type', '')

        if node_type == 'text':
            # Text node - extract the text content
            return adf_content.get('text', '')

        elif node_type == 'table':
            # Table node - extract table structure
            text_parts.append('\n--- TABLE START ---\n')

        elif node_type == 'tableRow':
            # Table row - add row separator
            text_parts.append('| ')

        elif node_type == 'tableCell' or node_type == 'tableHeader':
            # Table cell - extract cell content
            pass  # Content will be processed recursively

        elif node_type == 'paragraph':
            # Paragraph - add line break
            pass  # Content will be processed recursively

        # Recursively process content array
        content = adf_content.get('content', [])
        if isinstance(content, list):
            for item in content:
                extracted_text = self._extract_text_from_adf(item)
                if extracted_text:
                    text_parts.append(extracted_text)

                    # Add separators for table structure
                    if node_type == 'tableRow':
                        text_parts.append(' | ')

        # Join text parts and clean up
        result = ''.join(text_parts)

        # Add line breaks for table rows
        if node_type == 'tableRow':
            result += '\n'
        elif node_type == 'table':
            result += '\n--- TABLE END ---\n'

        return result

    def _parse_table_format(self, description_text: str) -> List[FirewallRule]:
        """
        Parse firewall rules from table format.

        Handles various table formats:
        - | Field | Value |
        - Field | Value
        - Field: Value (in table context)

        Args:
            description_text (str): Plain text description

        Returns:
            List[FirewallRule]: Parsed firewall rules
        """
        rules = []

        # Define regex patterns for different table formats
        patterns = [
            # Standard table format: | Field | Value |
            r'\|?\s*(Source|Destination|Port|Protocol|Action)\s*\|?\s*([^\|\n]+)',
            # Colon format: Field: Value
            r'(Source|Destination|Port|Protocol|Action)\s*:\s*([^\n]+)',
            # Simple format: Field Value (space separated)
            r'(Source|Destination|Port|Protocol|Action)\s+([^\n]+)'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, description_text, re.IGNORECASE)
            if matches:
                print(f"🔍 Found {len(matches)} field/value pairs using pattern")
                rules = self._group_matches_into_rules(matches)
                if rules:
                    break

        return rules

    def _parse_structured_format(self, description_text: str) -> List[FirewallRule]:
        """
        Parse firewall rules from structured text format.

        Handles formats like:
        - Rule 1: Source=********, Destination=***********, Port=80
        - Firewall Rule: src=******** dst=*********** port=80

        Args:
            description_text (str): Plain text description

        Returns:
            List[FirewallRule]: Parsed firewall rules
        """
        rules = []

        # Pattern for structured rule format
        rule_patterns = [
            # Pattern: src=value dst=value port=value
            r'(?:src|source)[=:\s]+([^\s,]+).*?(?:dst|destination)[=:\s]+([^\s,]+).*?(?:port)[=:\s]+([^\s,]+)',
            # Pattern: Source=value, Destination=value, Port=value
            r'Source[=:\s]+([^,\n]+).*?Destination[=:\s]+([^,\n]+).*?Port[=:\s]+([^,\n]+)'
        ]

        for pattern in rule_patterns:
            matches = re.findall(pattern, description_text, re.IGNORECASE | re.DOTALL)
            if matches:
                print(f"🔍 Found {len(matches)} structured rules")
                for match in matches:
                    if len(match) >= 3:
                        rule = FirewallRule(
                            source=match[0].strip(),
                            destination=match[1].strip(),
                            port=match[2].strip(),
                            protocol="TCP",  # Default protocol
                            action="allow"   # Default action
                        )
                        rules.append(rule)
                        print(f"✅ Created rule: {rule.source} -> {rule.destination}:{rule.port}")
                break

        return rules

    def _group_matches_into_rules(self, matches: List[tuple]) -> List[FirewallRule]:
        """
        Group field/value matches into complete firewall rules.

        Args:
            matches (List[tuple]): List of (field, value) tuples

        Returns:
            List[FirewallRule]: Complete firewall rules
        """
        rules = []
        current_rule = {}
        rule_count = 0

        for field, value in matches:
            # Clean up the field name and value
            field = field.strip().lower()
            value = value.strip().rstrip('|').strip()  # Remove trailing pipes

            # Only process recognized firewall rule fields
            if field in ['source', 'destination', 'port', 'protocol', 'action']:
                current_rule[field] = value
                print(f"   Found {field}: {value}")

                # Check if we have enough fields to create a complete rule
                # Minimum required: source, destination, port
                if len(current_rule) >= 3 and all(f in current_rule for f in ['source', 'destination', 'port']):
                    rule_count += 1

                    # Create a new FirewallRule object
                    rule = FirewallRule(
                        source=current_rule.get('source', ''),
                        destination=current_rule.get('destination', ''),
                        port=current_rule.get('port', ''),
                        protocol=current_rule.get('protocol', 'TCP'),  # Default to TCP
                        action=current_rule.get('action', 'allow')     # Default to allow
                    )

                    rules.append(rule)
                    print(f"✅ Created rule {rule_count}: {rule.source} -> {rule.destination}:{rule.port}")

                    # Reset for next rule
                    current_rule = {}

        return rules

    def validate_security_standards(self, rules: List[FirewallRule]) -> List[SecurityViolation]:
        """
        Validate firewall rules against built-in security standards.

        This method checks each firewall rule against a set of predefined
        security patterns to identify potential risks. It examines source,
        destination, port, and protocol fields for known risky configurations.

        Args:
            rules (List[FirewallRule]): List of firewall rules to validate

        Returns:
            List[SecurityViolation]: List of security violations found

        Security Checks Performed:
            1. Overly permissive sources (any, 0.0.0.0/0, *)
            2. Overly permissive destinations (any, 0.0.0.0/0, *)
            3. Risky protocols (SSH, RDP, Telnet, FTP)
            4. Wide port ranges (>100 ports)

        Customization:
            To modify security checks, edit the self.risky_patterns dictionary
            in the __init__ method. Each pattern has a regex and severity level.
        """
        print("🔒 Validating against security standards")
        print(f"📋 Checking {len(rules)} rules against {len(self.risky_patterns)} security patterns")

        violations = []

        # Check each rule against all security patterns
        for i, rule in enumerate(rules):
            print(f"   Checking rule {i+1}: {rule.source} -> {rule.destination}:{rule.port}")

            # Iterate through each security check pattern
            for check_name, check_config in self.risky_patterns.items():
                pattern = check_config['pattern']
                severity = check_config['severity']

                # Check SOURCE field for risky patterns
                if 'source' in check_name:
                    if re.search(pattern, rule.source, re.IGNORECASE):
                        violation = SecurityViolation(
                            rule_index=i,
                            field='source',
                            value=rule.source,
                            issue=f"Risky source pattern detected: {check_name.replace('_', ' ')}",
                            severity=severity,
                            recommendation="Consider restricting source to specific IP ranges or networks"
                        )
                        violations.append(violation)
                        print(f"     ⚠️  {severity} violation: {violation.issue}")

                # Check DESTINATION field for risky patterns
                elif 'destination' in check_name:
                    if re.search(pattern, rule.destination, re.IGNORECASE):
                        violation = SecurityViolation(
                            rule_index=i,
                            field='destination',
                            value=rule.destination,
                            issue=f"Risky destination pattern detected: {check_name.replace('_', ' ')}",
                            severity=severity,
                            recommendation="Consider restricting destination to specific IP ranges or networks"
                        )
                        violations.append(violation)
                        print(f"     ⚠️  {severity} violation: {violation.issue}")

                # Check PROTOCOL/PORT fields for risky patterns
                elif 'protocol' in check_name:
                    # Combine protocol and port for checking
                    protocol_port_string = f"{rule.protocol} {rule.port}"
                    if re.search(pattern, protocol_port_string, re.IGNORECASE):
                        violation = SecurityViolation(
                            rule_index=i,
                            field='protocol/port',
                            value=f"{rule.protocol}:{rule.port}",
                            issue=f"Risky protocol/port detected: {check_name.replace('_', ' ')}",
                            severity=severity,
                            recommendation="Consider using more secure protocols or restricting access"
                        )
                        violations.append(violation)
                        print(f"     ⚠️  {severity} violation: {violation.issue}")

                # Check for WIDE PORT RANGES (special case)
                elif 'port_range' in check_name:
                    match = re.search(pattern, rule.port)
                    if match:
                        # Extract start and end port numbers
                        start_port, end_port = match.groups()
                        try:
                            port_range_size = int(end_port) - int(start_port)

                            # Flag ranges wider than 100 ports as risky
                            if port_range_size > 100:
                                violation = SecurityViolation(
                                    rule_index=i,
                                    field='port',
                                    value=rule.port,
                                    issue=f"Wide port range detected: {port_range_size + 1} ports",
                                    severity=severity,
                                    recommendation="Consider narrowing port range to specific required ports"
                                )
                                violations.append(violation)
                                print(f"     ⚠️  {severity} violation: {violation.issue}")
                        except ValueError:
                            # Handle case where port numbers aren't valid integers
                            print(f"     ⚠️  Could not parse port range: {rule.port}")

        print(f"🔍 Security validation complete: found {len(violations)} violations")

        # Print summary by severity
        severity_counts = {}
        for violation in violations:
            severity_counts[violation.severity] = severity_counts.get(violation.severity, 0) + 1

        if severity_counts:
            print("📊 Violations by severity:")
            for severity, count in sorted(severity_counts.items()):
                print(f"   {severity}: {count}")

        return violations

    def query_tufin_rules(self, rules: List[FirewallRule]) -> Dict[str, Any]:
        """
        Query Tufin SecureTrack API for existing firewall rules and analysis.

        This method connects to Tufin SecureTrack using the REST API to:
        1. Authenticate with the Tufin system
        2. Search for existing rules that might conflict
        3. Analyze policy compliance
        4. Provide recommendations for rule implementation

        Args:
            rules (List[FirewallRule]): List of firewall rules to analyze

        Returns:
            Dict[str, Any]: Dictionary with analysis results for each rule

        Tufin API Endpoints Used:
            - POST /api/v1/auth/login - Authentication
            - GET /api/v1/devices - Get firewall devices
            - POST /api/v1/rules/search - Search existing rules
            - POST /api/v1/topology/path - Analyze connectivity paths

        Real Implementation Notes:
            This method now includes actual Tufin API integration patterns.
            For production use, you may need to adjust endpoints based on
            your Tufin SecureTrack version and configuration.
        """
        print("🔍 Querying Tufin SecureTrack for existing rules and analysis")

        # Check if Tufin configuration is available
        if not all([self.tufin_url, self.tufin_username, self.tufin_password]):
            print("⚠️  Tufin configuration not provided, skipping Tufin analysis")
            print("   To enable Tufin integration, set these environment variables:")
            print("   - TUFIN_URL (e.g., https://your-tufin-server)")
            print("   - TUFIN_USERNAME")
            print("   - TUFIN_PASSWORD")
            return {}

        print(f"🔗 Connecting to Tufin SecureTrack at: {self.tufin_url}")
        print(f"👤 Authenticating as user: {self.tufin_username}")

        # Authenticate with Tufin
        session_token = self._authenticate_tufin()
        if not session_token:
            print("❌ Failed to authenticate with Tufin")
            return {}

        tufin_results = {}

        # Get available firewall devices
        devices = self._get_tufin_devices(session_token)
        print(f"📡 Found {len(devices)} firewall devices in Tufin")

        # Analyze each firewall rule
        for i, rule in enumerate(rules):
            rule_key = f"rule_{i+1}"
            print(f"  🔍 Analyzing rule {i+1}: {rule.source} -> {rule.destination}:{rule.port}")

            try:
                # Search for existing rules
                existing_rules = self._search_tufin_rules(session_token, rule, devices)

                # Analyze connectivity paths
                path_analysis = self._analyze_tufin_paths(session_token, rule)

                # Check for policy violations
                policy_check = self._check_tufin_policy(session_token, rule)

                # Compile results
                tufin_results[rule_key] = {
                    "existing_rules": existing_rules,
                    "path_analysis": path_analysis,
                    "policy_compliance": policy_check,
                    "recommendations": self._generate_tufin_recommendations(
                        rule, existing_rules, path_analysis, policy_check
                    )
                }

                print(f"    ✅ Tufin analysis completed for rule {i+1}")

            except Exception as e:
                # Handle any errors during Tufin analysis
                print(f"    ❌ Failed to query Tufin for rule {i+1}: {e}")
                tufin_results[rule_key] = {
                    "error": str(e),
                    "recommendations": ["Manual review required due to Tufin query failure"]
                }

        # Clean up session
        self._logout_tufin(session_token)

        print(f"✅ Tufin analysis complete for {len(rules)} rules")
        return tufin_results

    def _authenticate_tufin(self) -> Optional[str]:
        """
        Authenticate with Tufin SecureTrack API.

        Returns:
            Optional[str]: Session token if successful, None if failed
        """
        try:
            auth_url = f"{self.tufin_url}/api/v1/auth/login"
            auth_data = {
                "username": self.tufin_username,
                "password": self.tufin_password
            }

            print("🔐 Authenticating with Tufin API...")
            response = requests.post(auth_url, json=auth_data, timeout=30, verify=False)
            response.raise_for_status()

            # Extract session token from response
            auth_result = response.json()
            session_token = auth_result.get('token') or auth_result.get('sessionId')

            if session_token:
                print("✅ Successfully authenticated with Tufin")
                return session_token
            else:
                print("❌ No session token received from Tufin")
                return None

        except requests.RequestException as e:
            print(f"❌ Tufin authentication failed: {e}")
            return None

    def _get_tufin_devices(self, session_token: str) -> List[Dict]:
        """
        Get list of firewall devices from Tufin.

        Args:
            session_token (str): Authenticated session token

        Returns:
            List[Dict]: List of firewall devices
        """
        try:
            devices_url = f"{self.tufin_url}/api/v1/devices"
            headers = {"Authorization": f"Bearer {session_token}"}

            response = requests.get(devices_url, headers=headers, timeout=30, verify=False)
            response.raise_for_status()

            devices_data = response.json()
            devices = devices_data.get('devices', []) if isinstance(devices_data, dict) else devices_data

            return devices[:10]  # Limit to first 10 devices for performance

        except requests.RequestException as e:
            print(f"⚠️  Failed to get Tufin devices: {e}")
            return []

    def _search_tufin_rules(self, session_token: str, rule: FirewallRule, devices: List[Dict]) -> List[Dict]:
        """
        Search for existing rules in Tufin that might conflict with the new rule.

        Args:
            session_token (str): Authenticated session token
            rule (FirewallRule): Rule to search for
            devices (List[Dict]): Available firewall devices

        Returns:
            List[Dict]: List of existing rules that might conflict
        """
        try:
            search_url = f"{self.tufin_url}/api/v1/rules/search"
            headers = {"Authorization": f"Bearer {session_token}"}

            # Build search criteria
            search_data = {
                "source": rule.source,
                "destination": rule.destination,
                "service": f"{rule.protocol}/{rule.port}",
                "devices": [device.get('id') for device in devices[:5]]  # Search first 5 devices
            }

            response = requests.post(search_url, json=search_data, headers=headers, timeout=30, verify=False)

            if response.status_code == 200:
                search_results = response.json()
                rules_found = search_results.get('rules', []) if isinstance(search_results, dict) else []
                return rules_found[:10]  # Limit to first 10 results
            else:
                print(f"⚠️  Tufin rule search returned status {response.status_code}")
                return []

        except requests.RequestException as e:
            print(f"⚠️  Failed to search Tufin rules: {e}")
            return []

    def _analyze_tufin_paths(self, session_token: str, rule: FirewallRule) -> Dict:
        """
        Analyze connectivity paths using Tufin topology analysis.

        Args:
            session_token (str): Authenticated session token
            rule (FirewallRule): Rule to analyze

        Returns:
            Dict: Path analysis results
        """
        try:
            path_url = f"{self.tufin_url}/api/v1/topology/path"
            headers = {"Authorization": f"Bearer {session_token}"}

            path_data = {
                "source": rule.source,
                "destination": rule.destination,
                "service": f"{rule.protocol}/{rule.port}"
            }

            response = requests.post(path_url, json=path_data, headers=headers, timeout=30, verify=False)

            if response.status_code == 200:
                return response.json()
            else:
                return {"status": "analysis_unavailable", "message": f"HTTP {response.status_code}"}

        except requests.RequestException as e:
            return {"status": "error", "message": str(e)}

    def _check_tufin_policy(self, session_token: str, rule: FirewallRule) -> Dict:
        """
        Check rule against Tufin security policies.

        Args:
            session_token (str): Authenticated session token
            rule (FirewallRule): Rule to check

        Returns:
            Dict: Policy compliance results
        """
        # For now, return a basic policy check structure
        # In a real implementation, this would call Tufin's policy checking APIs
        return {
            "compliant": True,
            "violations": [],
            "warnings": [],
            "policy_version": "unknown"
        }

    def _generate_tufin_recommendations(self, rule: FirewallRule, existing_rules: List[Dict],
                                       path_analysis: Dict, policy_check: Dict) -> List[str]:
        """
        Generate recommendations based on Tufin analysis results.

        Args:
            rule (FirewallRule): The rule being analyzed
            existing_rules (List[Dict]): Existing rules found
            path_analysis (Dict): Path analysis results
            policy_check (Dict): Policy compliance results

        Returns:
            List[str]: List of recommendations
        """
        recommendations = []

        # Recommendations based on existing rules
        if existing_rules:
            recommendations.append(f"Found {len(existing_rules)} existing rules that may be related")
            recommendations.append("Review existing rules to avoid duplication")
        else:
            recommendations.append("No conflicting rules found - new rule can be implemented")

        # Recommendations based on path analysis
        if path_analysis.get("status") == "blocked":
            recommendations.append("⚠️  Path analysis shows traffic may be blocked by existing rules")
        elif path_analysis.get("status") == "allowed":
            recommendations.append("✅ Path analysis shows traffic path is available")

        # Recommendations based on policy compliance
        if not policy_check.get("compliant", True):
            recommendations.append("⚠️  Rule may violate security policies")
            for violation in policy_check.get("violations", []):
                recommendations.append(f"Policy violation: {violation}")

        # General recommendations
        recommendations.append(f"Consider implementing rule on appropriate firewall device")
        recommendations.append(f"Enable logging for rule monitoring")

        return recommendations

    def _logout_tufin(self, session_token: str):
        """
        Clean up Tufin session.

        Args:
            session_token (str): Session token to invalidate
        """
        try:
            logout_url = f"{self.tufin_url}/api/v1/auth/logout"
            headers = {"Authorization": f"Bearer {session_token}"}

            requests.post(logout_url, headers=headers, timeout=10, verify=False)
            print("🔓 Tufin session cleaned up")

        except Exception:
            # Ignore logout errors - session will expire anyway
            pass

    def generate_recommendations(self, violations: List[SecurityViolation], tufin_results: Dict) -> List[str]:
        """
        Generate comprehensive security recommendations based on analysis results.

        This method combines findings from security validation and Tufin analysis
        to create a formatted list of recommendations that will be posted to Jira.

        Args:
            violations (List[SecurityViolation]): Security violations found during validation
            tufin_results (Dict): Results from Tufin API analysis

        Returns:
            List[str]: Formatted recommendation strings ready for Jira comment

        Recommendation Structure:
            1. Security violations (if any) with severity indicators
            2. Tufin analysis results (if available)
            3. General recommendations or "all clear" message

        Customization:
            - Modify severity_emoji dict to change visual indicators
            - Add additional recommendation logic based on your needs
            - Customize formatting for different Jira markdown styles
        """
        print("📝 Generating security recommendations")
        print(f"📊 Processing {len(violations)} violations and {len(tufin_results)} Tufin results")

        recommendations = []

        # SECTION 1: Security Violation Recommendations
        if violations:
            recommendations.append("🔒 **Security Violations Found:**")
            recommendations.append("")  # Add blank line for readability

            # Group violations by severity for better organization
            violations_by_severity = {}
            for violation in violations:
                severity = violation.severity
                if severity not in violations_by_severity:
                    violations_by_severity[severity] = []
                violations_by_severity[severity].append(violation)

            # Define emoji mapping for different severity levels
            severity_emoji = {
                'CRITICAL': '🚨',  # Red alert for critical issues
                'HIGH': '⚠️',      # Warning for high-priority issues
                'MEDIUM': '⚡',    # Lightning for medium-priority issues
                'LOW': 'ℹ️'        # Info icon for low-priority issues
            }

            # Process violations in order of severity (most severe first)
            severity_order = ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']
            for severity in severity_order:
                if severity in violations_by_severity:
                    for violation in violations_by_severity[severity]:
                        emoji = severity_emoji.get(violation.severity, 'ℹ️')

                        # Format the violation with rule number and details
                        recommendations.append(
                            f"{emoji} **Rule {violation.rule_index + 1}** - {violation.field}: {violation.issue}"
                        )
                        recommendations.append(f"   *Recommendation:* {violation.recommendation}")
                        recommendations.append("")  # Blank line between violations

        # SECTION 2: Tufin Analysis Recommendations
        if tufin_results:
            recommendations.append("🔍 **Tufin Analysis Results:**")
            recommendations.append("")

            for rule_key, result in tufin_results.items():
                rule_number = rule_key.replace('rule_', '')

                # Handle errors in Tufin analysis
                if 'error' in result:
                    recommendations.append(f"⚠️ **Rule {rule_number}**: Tufin analysis failed - {result['error']}")
                    if 'recommendations' in result:
                        for rec in result['recommendations']:
                            recommendations.append(f"   • {rec}")
                    recommendations.append("")
                    continue

                # Process successful Tufin analysis
                recommendations.append(f"📋 **Rule {rule_number} Analysis:**")

                # Add existing rules information
                if 'existing_rules' in result and result['existing_rules']:
                    recommendations.append(f"   *Existing Rules:* {len(result['existing_rules'])} related rules found")

                # Add conflict information
                if 'conflicts' in result and result['conflicts']:
                    for conflict in result['conflicts']:
                        recommendations.append(f"   ⚠️ *Conflict:* {conflict.get('message', 'Unknown conflict')}")

                # Add Tufin recommendations
                if 'recommendations' in result:
                    for rec in result['recommendations']:
                        recommendations.append(f"   • {rec}")

                recommendations.append("")  # Blank line between rules

        # SECTION 3: General Recommendations or All-Clear Message
        if not violations and not tufin_results:
            recommendations.append("✅ **Analysis Complete - No Issues Found**")
            recommendations.append("")
            recommendations.append("The firewall change request appears to comply with security standards.")
            recommendations.append("No security violations or conflicts were detected.")
        elif not violations and tufin_results:
            recommendations.append("✅ **Security Standards Compliance**")
            recommendations.append("")
            recommendations.append("No security standard violations detected. Review Tufin analysis above for implementation guidance.")

        # Add footer with analysis metadata
        recommendations.append("")
        recommendations.append("---")
        recommendations.append("*This analysis was generated automatically by the Firewall Security Analyzer*")

        print(f"✅ Generated {len(recommendations)} recommendation lines")
        return recommendations

    def post_jira_comment(self, issue_key: str, recommendations: List[str]) -> bool:
        """
        Post security recommendations as a comment to the Jira issue.

        This method takes the generated recommendations and posts them as a
        formatted comment on the original Jira issue. This provides immediate
        feedback to the requestor about security concerns.

        Args:
            issue_key (str): The Jira issue key to comment on
            recommendations (List[str]): List of recommendation strings to post

        Returns:
            bool: True if comment was posted successfully, False otherwise

        Comment Format:
            - Header with analysis title
            - All recommendations joined with newlines
            - Footer with timestamp and automation notice

        API Details:
            - Uses Jira REST API v2 comment endpoint
            - Authenticates with username and API token
            - Sends comment as JSON payload
            - Has 30-second timeout

        Error Handling:
            - Catches all HTTP-related errors
            - Logs specific error messages
            - Returns False on any failure
        """
        print(f"💬 Posting recommendations to Jira issue: {issue_key}")
        print(f"📝 Comment will contain {len(recommendations)} lines")

        # Build the comment body with header and footer
        comment_lines = [
            "🔒 **Automated Firewall Security Analysis**",
            "",  # Blank line for readability
        ]

        # Add all recommendations
        comment_lines.extend(recommendations)

        # Add footer with timestamp
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        comment_lines.extend([
            "",
            f"_Analysis generated automatically on {timestamp}_"
        ])

        # Join all lines into a single comment body
        comment_body = "\n".join(comment_lines)

        # Construct the Jira comment API URL
        url = f"{self.jira_url}/rest/api/2/issue/{issue_key}/comment"

        # Set up authentication
        auth = (self.jira_username, self.jira_token)

        # Prepare the JSON payload
        # For Jira Cloud v3 API, we can use either plain text or ADF format
        # Using plain text for simplicity and broader compatibility
        payload = {
            "body": comment_body,
            # Alternative ADF format (commented out for simplicity):
            # "body": {
            #     "type": "doc",
            #     "version": 1,
            #     "content": [
            #         {
            #             "type": "paragraph",
            #             "content": [
            #                 {
            #                     "type": "text",
            #                     "text": comment_body
            #                 }
            #             ]
            #         }
            #     ]
            # }
        }

        try:
            print(f"🌐 Making API call to: {url}")

            # Make the HTTP POST request to add the comment
            response = requests.post(url, auth=auth, json=payload, timeout=30)

            # Check if the request was successful
            response.raise_for_status()

            # Parse response to get comment details
            comment_data = response.json()
            comment_id = comment_data.get('id', 'unknown')

            print(f"✅ Successfully posted comment to Jira (Comment ID: {comment_id})")
            print(f"📄 Comment length: {len(comment_body)} characters")
            return True

        except requests.RequestException as e:
            # Handle HTTP errors, authentication failures, network issues, etc.
            print(f"❌ Failed to post comment to Jira: {e}")
            print(f"   Issue: {issue_key}")
            print(f"   URL: {url}")
            print(f"   Check your Jira credentials and permissions")
            return False

    def analyze(self, issue_key: str, dry_run: bool = False) -> bool:
        """
        Execute the complete firewall analysis workflow.

        This is the main orchestration method that coordinates all the analysis
        steps from retrieving the Jira issue to posting recommendations back.

        Workflow Steps:
            1. Retrieve Jira issue data via API
            2. Parse firewall rules from issue description
            3. Validate rules against security standards
            4. Query Tufin for existing rule analysis (optional)
            5. Generate comprehensive recommendations
            6. Display results to console
            7. Post recommendations to Jira (unless dry run)

        Args:
            issue_key (str): Jira issue key to analyze (e.g., 'FW-123')
            dry_run (bool): If True, don't post comments to Jira (default: False)

        Returns:
            bool: True if analysis completed successfully, False if any step failed

        Error Handling:
            - Each step is validated before proceeding to the next
            - Failures are logged with specific error messages
            - Returns False immediately on critical failures
            - Continues with warnings for non-critical issues

        Usage Examples:
            analyzer.analyze('FW-123')  # Full analysis with Jira comment
            analyzer.analyze('FW-123', dry_run=True)  # Analysis only, no comment
        """
        print(f"🚀 Starting firewall analysis for {issue_key}")
        print(f"🔧 Mode: {'DRY RUN' if dry_run else 'LIVE RUN'}")
        print("=" * 60)

        # STEP 1: Retrieve Jira Issue Data
        print("\n📋 STEP 1: Retrieving Jira Issue")
        print("-" * 30)
        issue_data = self.get_jira_issue(issue_key)
        if not issue_data:
            print("❌ CRITICAL: Cannot proceed without Jira issue data")
            return False

        # STEP 2: Parse Firewall Rules from Issue Description
        print("\n🔍 STEP 2: Parsing Firewall Rules")
        print("-" * 30)
        rules = self.parse_firewall_table(issue_data)
        if not rules:
            print("❌ CRITICAL: No firewall rules found to analyze")
            print("   Check that the issue description contains a properly formatted table")
            return False

        print(f"✅ Successfully parsed {len(rules)} firewall rules")

        # STEP 3: Validate Against Security Standards
        print("\n🔒 STEP 3: Security Standards Validation")
        print("-" * 30)
        violations = self.validate_security_standards(rules)

        if violations:
            print(f"⚠️  Found {len(violations)} security violations")
        else:
            print("✅ No security violations detected")

        # STEP 4: Query Tufin for Existing Rules (Optional)
        print("\n🔍 STEP 4: Tufin Analysis")
        print("-" * 30)
        tufin_results = self.query_tufin_rules(rules)

        if tufin_results:
            print(f"✅ Tufin analysis completed for {len(tufin_results)} rules")
        else:
            print("ℹ️  Tufin analysis skipped (configuration not available)")

        # STEP 5: Generate Comprehensive Recommendations
        print("\n📝 STEP 5: Generating Recommendations")
        print("-" * 30)
        recommendations = self.generate_recommendations(violations, tufin_results)
        print(f"✅ Generated {len(recommendations)} recommendation lines")

        # STEP 6: Display Results to Console
        print("\n" + "=" * 60)
        print("📊 ANALYSIS RESULTS")
        print("=" * 60)
        for rec in recommendations:
            print(rec)
        print("=" * 60)

        # STEP 7: Post Results to Jira (Unless Dry Run)
        print("\n💬 STEP 7: Posting Results")
        print("-" * 30)
        if not dry_run:
            print("📤 Posting recommendations to Jira...")
            success = self.post_jira_comment(issue_key, recommendations)
            if not success:
                print("❌ CRITICAL: Failed to post comment to Jira")
                return False
            print("✅ Successfully posted recommendations to Jira")
        else:
            print("🔍 DRY RUN: Skipping Jira comment posting")
            print("   In live mode, recommendations would be posted as a comment")

        # FINAL SUMMARY
        print("\n" + "=" * 60)
        print("🎉 ANALYSIS COMPLETE")
        print("=" * 60)
        print(f"Issue: {issue_key}")
        print(f"Rules Analyzed: {len(rules)}")
        print(f"Violations Found: {len(violations)}")
        print(f"Tufin Results: {len(tufin_results)}")
        print(f"Mode: {'DRY RUN' if dry_run else 'LIVE RUN'}")
        print("✅ Analysis completed successfully")

        return True


def main():
    """
    Main entry point for the command-line interface.

    This function handles:
    - Command-line argument parsing
    - Analyzer initialization
    - Configuration validation
    - Workflow execution
    - Exit code management

    Command-Line Arguments:
        --jira-issue: Required. The Jira issue key to analyze (e.g., FW-123)
        --dry-run: Optional. Run analysis without posting comments to Jira

    Exit Codes:
        0: Success - Analysis completed without errors
        1: Failure - Configuration invalid or analysis failed

    Environment Variables Required:
        JIRA_URL: Your Jira instance URL
        JIRA_USERNAME: Your Jira username
        JIRA_API_TOKEN: Your Jira API token

    Environment Variables Optional:
        TUFIN_URL: Tufin SecureTrack URL
        TUFIN_USERNAME: Tufin username
        TUFIN_PASSWORD: Tufin password

    Usage Examples:
        python simple_firewall_analyzer.py --jira-issue FW-123
        python simple_firewall_analyzer.py --jira-issue PROJ-456 --dry-run
    """
    # Set up command-line argument parsing
    parser = argparse.ArgumentParser(
        description='Simple Firewall Change Analyzer - Automated security analysis for Jira firewall requests',
        epilog='''
Examples:
  %(prog)s --jira-issue FW-123
  %(prog)s --jira-issue PROJ-456 --dry-run

Environment Variables:
  JIRA_URL          Jira instance URL (required)
  JIRA_USERNAME     Jira username (required)
  JIRA_API_TOKEN    Jira API token (required)
  TUFIN_URL         Tufin API URL (optional)
  TUFIN_USERNAME    Tufin username (optional)
  TUFIN_PASSWORD    Tufin password (optional)
        ''',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    # Define command-line arguments
    parser.add_argument(
        '--jira-issue',
        required=True,
        help='Jira issue key to analyze (e.g., FW-123, PROJ-456)',
        metavar='ISSUE_KEY'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Run analysis without posting comments to Jira (useful for testing)'
    )

    # Parse command-line arguments
    args = parser.parse_args()

    print("🔒 Simple Firewall Change Analyzer")
    print("=" * 50)
    print(f"Issue Key: {args.jira_issue}")
    print(f"Mode: {'DRY RUN' if args.dry_run else 'LIVE RUN'}")
    print("=" * 50)

    try:
        # Initialize the analyzer
        print("🔧 Initializing analyzer...")
        analyzer = SimpleFirewallAnalyzer()

        # Validate configuration before proceeding
        print("✅ Validating configuration...")
        if not analyzer.validate_config():
            print("\n❌ Configuration validation failed")
            print("Please check your environment variables and try again")
            sys.exit(1)

        # Run the complete analysis workflow
        print("🚀 Starting analysis workflow...")
        success = analyzer.analyze(args.jira_issue, args.dry_run)

        # Handle results and set appropriate exit code
        if success:
            print(f"\n🎉 Analysis completed successfully for {args.jira_issue}")
            if args.dry_run:
                print("💡 This was a dry run - no changes were made to Jira")
            else:
                print("✅ Recommendations have been posted to the Jira issue")
            sys.exit(0)
        else:
            print(f"\n❌ Analysis failed for {args.jira_issue}")
            print("Check the error messages above for details")
            sys.exit(1)

    except KeyboardInterrupt:
        # Handle user interruption (Ctrl+C)
        print("\n⏹️  Analysis interrupted by user")
        sys.exit(130)  # Standard exit code for SIGINT

    except Exception as e:
        # Handle any unexpected errors
        print(f"\n💥 Unexpected error occurred: {e}")
        print("This may indicate a bug in the script or an environmental issue")
        sys.exit(1)


# Entry point when script is run directly
if __name__ == '__main__':
    main()
