"""
Mock data for testing firewall automation without external dependencies.
"""

from datetime import datetime
from typing import Dict, Any, List


class MockJiraData:
    """Mock Jira issue data for testing."""

    @staticmethod
    def get_sample_issue_good() -> Dict[str, Any]:
        """Sample Jira issue with compliant firewall rule."""
        return {
            'key': 'FW-123',
            'summary': 'Allow HTTPS access to web server',
            'description': '''Firewall Change Request - Web Server Access

||Field||Value||
|Source|*************|
|Destination|*********|
|Port|443|
|Protocol|HTTPS|
|Action|Allow|
|Business Justification|Production web application requires secure access to backend API server for customer transactions. Approved by security team under change request CR-2024-001.|
|Requestor|<EMAIL>|
|Change Window|2024-01-15 02:00-04:00|
            ''',
            'status': 'In Progress',
            'priority': 'Medium',
            'assignee': '<PERSON>',
            'reporter': '<PERSON>',
            'created': '2024-01-10T10:00:00.000Z',
            'updated': '2024-01-10T15:30:00.000Z',
            'comments': [
                {
                    'author': 'Security Team',
                    'body': 'Initial security review completed. Approved for implementation.',
                    'created': '2024-01-10T14:00:00.000Z'
                }
            ]
        }

    @staticmethod
    def get_sample_issue_risky() -> Dict[str, Any]:
        """Sample Jira issue with high-risk firewall rule."""
        return {
            'key': 'FW-456',
            'summary': 'Emergency database access',
            'description': '''URGENT: Database Access Required

||Field||Value||
|Source|*|
|Destination|**********|
|Port|1433|
|Protocol|TCP|
|Action|Allow|
|Business Justification|testing|
|Requestor|<EMAIL>|
            ''',
            'status': 'Open',
            'priority': 'High',
            'assignee': 'Emergency Team',
            'reporter': 'Temp User',
            'created': '2024-01-10T18:00:00.000Z',
            'updated': '2024-01-10T18:05:00.000Z',
            'comments': []
        }

    @staticmethod
    def get_sample_issue_suspicious() -> Dict[str, Any]:
        """Sample Jira issue with suspicious patterns."""
        return {
            'key': 'FW-789',
            'summary': 'P2P application access',
            'description': '''
            File Sharing Application Setup

            | Field | Value |
            |-------|-------|
            | Source | ***********/24 |
            | Destination | internet |
            | Port | 6881 |
            | Protocol | TCP |
            | Action | Allow |
            | Business Justification | Need to download files quickly for project |
            | Requestor | <EMAIL> |
            ''',
            'status': 'Open',
            'priority': 'Low',
            'assignee': 'Network Team',
            'reporter': 'Regular User',
            'created': '2024-01-10T12:00:00.000Z',
            'updated': '2024-01-10T12:30:00.000Z',
            'comments': []
        }

    @staticmethod
    def get_sample_issue_critical() -> Dict[str, Any]:
        """Sample Jira issue with critical security violations."""
        return {
            'key': 'FW-999',
            'summary': 'Legacy system access',
            'description': '''Legacy System Maintenance Access

Source: any
Destination: **********
Port: 23
Protocol: telnet
Action: allow
Business Justification: quick fix needed
            ''',
            'status': 'Open',
            'priority': 'Critical',
            'assignee': 'Legacy Team',
            'reporter': 'System Admin',
            'created': '2024-01-10T20:00:00.000Z',
            'updated': '2024-01-10T20:15:00.000Z',
            'comments': [
                {
                    'author': 'Security Team',
                    'body': 'This request requires immediate security review.',
                    'created': '2024-01-10T20:10:00.000Z'
                }
            ]
        }

    @staticmethod
    def get_sample_issue_attack_pattern() -> Dict[str, Any]:
        """Sample Jira issue with attack-like patterns."""
        return {
            'key': 'FW-666',
            'summary': 'Special application port access',
            'description': '''Custom Application Access

||Field||Value||
|Source|**************|
|Destination|**********|
|Port|31337|
|Protocol|TCP|
|Action|Allow|
|Business Justification|Custom application requires this specific port for communication|
|Requestor|<EMAIL>|
            ''',
            'status': 'Open',
            'priority': 'Medium',
            'assignee': 'Security Team',
            'reporter': 'Developer',
            'created': '2024-01-10T16:00:00.000Z',
            'updated': '2024-01-10T16:30:00.000Z',
            'comments': []
        }


class MockTufinData:
    """Mock Tufin API response data for testing."""

    @staticmethod
    def get_existing_rules() -> List[Dict[str, Any]]:
        """Mock existing firewall rules from Tufin."""
        return [
            {
                'id': 'rule_001',
                'source': '***********/24',
                'destination': '*********',
                'port': '443',
                'protocol': 'HTTPS',
                'action': 'allow',
                'device': 'firewall-01',
                'created': '2023-12-01T10:00:00Z',
                'last_modified': '2023-12-01T10:00:00Z'
            },
            {
                'id': 'rule_002',
                'source': '192.168.2.0/24',
                'destination': '**********',
                'port': '1433',
                'protocol': 'TCP',
                'action': 'allow',
                'device': 'firewall-02',
                'created': '2023-11-15T14:30:00Z',
                'last_modified': '2023-11-15T14:30:00Z'
            },
            {
                'id': 'rule_003',
                'source': '10.0.1.0/24',
                'destination': '*********',
                'port': '80',
                'protocol': 'HTTP',
                'action': 'allow',
                'device': 'firewall-01',
                'created': '2023-10-20T09:15:00Z',
                'last_modified': '2023-12-05T16:45:00Z'
            }
        ]

    @staticmethod
    def get_overlapping_rules() -> List[Dict[str, Any]]:
        """Mock overlapping rules that might conflict."""
        return [
            {
                'id': 'rule_101',
                'source': '192.168.0.0/16',
                'destination': '*********',
                'port': '443',
                'protocol': 'HTTPS',
                'action': 'allow',
                'device': 'firewall-01'
            },
            {
                'id': 'rule_102',
                'source': '172.16.1.0/24',
                'destination': '*********',
                'port': '443',
                'protocol': 'HTTPS',
                'action': 'allow',
                'device': 'firewall-01'
            }
        ]

    @staticmethod
    def get_policy_violations() -> List[Dict[str, Any]]:
        """Mock policy violations from Tufin analysis."""
        return [
            {
                'type': 'overly_permissive_source',
                'severity': 'high',
                'message': 'Source address is too permissive',
                'recommendation': 'Specify exact source networks'
            },
            {
                'type': 'unencrypted_protocol',
                'severity': 'medium',
                'message': 'Protocol does not use encryption',
                'recommendation': 'Use encrypted alternative'
            }
        ]

    @staticmethod
    def get_devices() -> List[Dict[str, Any]]:
        """Mock firewall devices from Tufin."""
        return [
            {
                'id': 'fw-001',
                'name': 'firewall-01',
                'type': 'Cisco ASA',
                'location': 'DMZ',
                'status': 'active'
            },
            {
                'id': 'fw-002',
                'name': 'firewall-02',
                'type': 'Palo Alto',
                'location': 'Internal',
                'status': 'active'
            }
        ]


class MockEnvironmentData:
    """Mock environment configuration for testing."""

    @staticmethod
    def get_test_env_vars() -> Dict[str, str]:
        """Mock environment variables for testing."""
        return {
            'JIRA_URL': 'https://test-jira.company.com',
            'JIRA_USERNAME': '<EMAIL>',
            'JIRA_API_TOKEN': 'mock-api-token-12345',
            'TUFIN_URL': 'https://test-tufin.company.com',
            'TUFIN_USERNAME': 'test.tufin.user',
            'TUFIN_PASSWORD': 'mock-password-67890',
            'LOG_LEVEL': 'DEBUG',
            'DRY_RUN': 'true',
            'OUTPUT_DIR': './test_output'
        }


# Test scenarios for different risk levels
TEST_SCENARIOS = {
    'compliant': {
        'name': 'Compliant Web Server Access',
        'jira_data': MockJiraData.get_sample_issue_good(),
        'expected_risk_score': 0,
        'expected_violations': 0,
        'description': 'Well-formed request with proper justification'
    },
    'high_risk': {
        'name': 'High Risk Database Access',
        'jira_data': MockJiraData.get_sample_issue_risky(),
        'expected_risk_score': 25,  # Wildcard source + database port + weak justification
        'expected_violations': 3,
        'description': 'Wildcard source, database port, weak justification'
    },
    'suspicious': {
        'name': 'Suspicious P2P Access',
        'jira_data': MockJiraData.get_sample_issue_suspicious(),
        'expected_risk_score': 15,  # P2P port + wildcard destination
        'expected_violations': 2,
        'description': 'P2P application with internet access'
    },
    'critical': {
        'name': 'Critical Legacy Protocol',
        'jira_data': MockJiraData.get_sample_issue_critical(),
        'expected_risk_score': 30,  # Critical protocol + wildcard + weak justification
        'expected_violations': 4,
        'description': 'Telnet with wildcard source and weak justification'
    },
    'attack_pattern': {
        'name': 'Potential Attack Pattern',
        'jira_data': MockJiraData.get_sample_issue_attack_pattern(),
        'expected_risk_score': 8,  # Suspicious port
        'expected_violations': 1,
        'description': 'Elite/Leet port commonly used in attacks'
    }
}
