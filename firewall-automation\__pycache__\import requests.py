import requests
import pandas as pd
import time

LEAGUE_ID = 623787

# API URLs
STANDINGS_URL = f"https://fantasy.premierleague.com/api/leagues-classic/{LEAGUE_ID}/standings/"
BOOTSTRAP_STATIC_URL = "https://fantasy.premierleague.com/api/bootstrap-static/"

# === Get League Standings ===
def get_league_standings(league_id):
    all_results = []
    page = 1
    while True:
        url = f"https://fantasy.premierleague.com/api/leagues-classic/{league_id}/standings/?page_standings={page}"
        res = requests.get(url)
        data = res.json()
        results = data['standings']['results']
        all_results.extend(results)
        if data['standings']['has_next']:
            page += 1
            time.sleep(1)  # Be kind to the API
        else:
            break
    return pd.DataFrame(all_results)

# === Get Gameweek Scores ===
def get_gameweek_data():
    res = requests.get(BOOTSTRAP_STATIC_URL)
    data = res.json()
    events = data['events']
    gw_data = [{
        "id": event["id"],
        "name": event["name"],
        "average_score": event["average_entry_score"],
        "highest_score": event["highest_score"]
    } for event in events if event["finished"]]
    return pd.DataFrame(gw_data)

# === Run & Save Outputs ===
standings_df = get_league_standings(LEAGUE_ID)
gw_df = get_gameweek_data()

# Keep key columns
standings_df = standings_df[['entry_name', 'player_name', 'rank', 'total']].sort_values(by='rank')
gw_df = gw_df.sort_values(by='id')

# Save to CSV
standings_df.to_csv("fpl_league_standings.csv", index=False)
gw_df.to_csv("fpl_gameweek_scores.csv", index=False)

print("✅ Data saved: fpl_league_standings.csv & fpl_gameweek_scores.csv")