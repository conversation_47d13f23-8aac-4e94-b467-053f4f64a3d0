# Simple Firewall Analyzer Dependencies
# =====================================
#
# This file contains the minimal dependencies required for the
# simple firewall analyzer to function.
#
# PHILOSOPHY:
#   Keep dependencies minimal to reduce complexity, security risks,
#   and installation time. Only include what is absolutely necessary.
#
# DEPENDENCIES EXPLAINED:
#
# requests - HTTP library for API calls
# --------------------------------------
# Used for:
#   - Connecting to Jira REST API to retrieve issues
#   - Posting comments back to Jira issues
#   - Connecting to Tufin API for firewall rule analysis (optional)
#
# Why this version:
#   - 2.25.0+ includes important security fixes
#   - Stable, well-tested version with broad compatibility
#   - Includes all features needed for our API interactions
#
# Alternative considered:
#   - urllib (built-in): More complex to use, no JSON handling
#   - httpx: More modern but adds unnecessary complexity
#   - aiohttp: Async features not needed for our use case

requests>=2.25.0

# INSTALLATION:
#   pip install -r simple_requirements.txt
#
# VERIFICATION:
#   python -c "import requests; print(f'Requests version: {requests.__version__}')"
#
# SECURITY NOTE:
#   Always use the latest compatible version to get security updates.
#   Check for updates regularly: pip list --outdated
