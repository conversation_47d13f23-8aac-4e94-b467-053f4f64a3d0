# Simple Azure DevOps Pipeline for Firewall Change Automation
# ================================================================
#
# PURPOSE:
#   This pipeline provides automated security analysis for Jira firewall
#   change requests using the simplified single-script analyzer.
#
# FEATURES:
#   - Manual trigger only (no automatic builds)
#   - Parameter-driven execution
#   - Minimal dependencies (just Python + requests)
#   - Single-stage execution for simplicity
#   - Environment variable configuration
#   - Dry-run capability for testing
#
# USAGE:
#   1. Set up pipeline variables for Jira/Tufin credentials
#   2. Run pipeline with required parameters
#   3. Review analysis results in pipeline output
#   4. Check Jira issue for posted recommendations (if not dry-run)
#
# REQUIRED PIPELINE VARIABLES:
#   - JIRA_URL: Your Jira instance URL
#   - JIRA_USERNAME: Jira username
#   - JIRA_API_TOKEN: Jira API token
#
# OPTIONAL PIPELINE VARIABLES:
#   - TUFIN_URL: Tufin SecureTrack URL
#   - TUFIN_USERNAME: Tufin username
#   - TUFIN_PASSWORD: Tufin password

# PIPELINE CONFIGURATION
# ======================

# Trigger: Manual only to prevent accidental runs
trigger: none

# Runtime Parameters
# These are provided by the user when running the pipeline
parameters:
- name: jiraIssue
  displayName: 'Jira Issue Key'
  type: string
  default: ''
  # Example: FW-123, PROJ-456, etc.

- name: dryRun
  displayName: 'Dry Run (do not post comments)'
  type: boolean
  default: true
  # Default to dry-run for safety

# Pipeline Variables
# These control the execution environment
variables:
  pythonVersion: '3.11'  # Python version to use

# Execution Environment
# Use Ubuntu for consistent, fast execution
pool:
  vmImage: 'ubuntu-latest'

# PIPELINE STEPS
# ==============

steps:
# STEP 1: Validate Input Parameters
# ---------------------------------
# Ensures that required parameters are provided and properly formatted
# before proceeding with the analysis
- script: |
    echo "🔍 Validating input parameters..."

    # Check if Jira issue key is provided
    if [ -z "${{ parameters.jiraIssue }}" ]; then
      echo "##vso[task.logissue type=error]Jira Issue Key is required"
      echo "Please provide a valid Jira issue key (e.g., FW-123)"
      exit 1
    fi

    # Validate Jira issue key format (PROJECT-NUMBER)
    if [[ ! "${{ parameters.jiraIssue }}" =~ ^[A-Z]+-[0-9]+$ ]]; then
      echo "##vso[task.logissue type=error]Invalid Jira Issue Key format"
      echo "Expected format: PROJECT-123 (e.g., FW-123, PROJ-456)"
      echo "Provided: ${{ parameters.jiraIssue }}"
      exit 1
    fi

    # Display validated parameters
    echo "✅ Input validation passed"
    echo "📋 Pipeline Parameters:"
    echo "   Jira Issue: ${{ parameters.jiraIssue }}"
    echo "   Dry Run: ${{ parameters.dryRun }}"
    echo "   Python Version: $(pythonVersion)"
  displayName: 'Validate Parameters'

# STEP 2: Set Up Python Environment
# ----------------------------------
# Configures the Python runtime environment for the analyzer
- task: UsePythonVersion@0
  inputs:
    versionSpec: '$(pythonVersion)'
    addToPath: true
    architecture: 'x64'
  displayName: 'Use Python $(pythonVersion)'

# STEP 3: Install Dependencies
# -----------------------------
# Installs the minimal required Python packages
# Only 'requests' is needed for the simple analyzer
- script: |
    echo "📦 Installing Python dependencies..."
    cd firewall-automation

    # Install from simple requirements file (just 'requests')
    pip install -r simple_requirements.txt

    echo "✅ Dependencies installed successfully"
    pip list | grep requests  # Verify requests is installed
  displayName: 'Install Dependencies'

# STEP 4: Run Firewall Analysis
# ------------------------------
# Executes the main analyzer script with provided parameters
# Environment variables are injected from pipeline variables
- script: |
    echo "🚀 Starting firewall analysis..."
    cd firewall-automation

    # Build command with conditional dry-run flag
    python simple_firewall_analyzer.py \
      --jira-issue "${{ parameters.jiraIssue }}" \
      ${{ eq(parameters.dryRun, true) && '--dry-run' || '' }}

    echo "✅ Analysis execution completed"
  env:
    # Jira Configuration (Required)
    JIRA_URL: $(JIRA_URL)
    JIRA_USERNAME: $(JIRA_USERNAME)
    JIRA_API_TOKEN: $(JIRA_API_TOKEN)

    # Tufin Configuration (Optional)
    TUFIN_URL: $(TUFIN_URL)
    TUFIN_USERNAME: $(TUFIN_USERNAME)
    TUFIN_PASSWORD: $(TUFIN_PASSWORD)
  displayName: 'Run Firewall Analysis'

# STEP 5: Generate Pipeline Summary
# ----------------------------------
# Provides a clear summary of what was accomplished
# Helps users understand the results and next steps
- script: |
    echo "📊 Generating pipeline summary..."
    echo ""
    echo "## 📊 Firewall Analysis Complete"
    echo ""
    echo "**Analysis Details:**"
    echo "- **Jira Issue:** ${{ parameters.jiraIssue }}"
    echo "- **Analysis Date:** $(date)"
    echo "- **Execution Mode:** ${{ parameters.dryRun == true && 'DRY RUN' || 'LIVE RUN' }}"
    echo "- **Python Version:** $(python --version)"
    echo ""

    # Provide mode-specific information
    if [ "${{ parameters.dryRun }}" = "true" ]; then
      echo "**🔍 Dry Run Results:**"
      echo "- Analysis completed successfully"
      echo "- No comments were posted to Jira"
      echo "- Review the analysis output above"
      echo "- Re-run without dry-run flag to post results"
    else
      echo "**✅ Live Run Results:**"
      echo "- Analysis completed successfully"
      echo "- Recommendations have been posted to the Jira issue"
      echo "- Check the Jira issue for detailed security analysis"
      echo "- Review any violations and implement recommendations"
    fi

    echo ""
    echo "**Next Steps:**"
    if [ "${{ parameters.dryRun }}" = "true" ]; then
      echo "1. Review the analysis results above"
      echo "2. If satisfied, re-run pipeline with dry-run disabled"
      echo "3. Check Jira issue for posted recommendations"
    else
      echo "1. Review the posted recommendations in Jira"
      echo "2. Address any security violations identified"
      echo "3. Implement suggested firewall rule improvements"
    fi
  displayName: 'Pipeline Summary'
